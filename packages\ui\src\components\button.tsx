"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@workspace/ui/lib/utils";
import BouncingCircles from "@workspace/ui/components/icons/bouncing-circles";
import FadeStaggerCircles from "@workspace/ui/components/icons/fade-stagger-circles";
import { TooltipNotification } from "@workspace/ui/components/tooltip-notification";
import { Tooltip } from "@workspace/ui/components/tooltip";

const buttonVariants = cva(
  "relative inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-xl font-bold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border-border drop-shadow-black/30 fake-text-stroke border-2 inset-ring-4 focus-visible:outline-3 select-none",
  {
    variants: {
      variant: {
        default: "bg-background inset-ring-ring",
        destructive:
          "bg-destructive text-white shadow-xs focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
        primary: "bg-primary inset-ring-ring/30",
        secondary:
          "bg-foreground/80 text-background text-shadow-none inset-ring-ring/20",
      },
      size: {
        default: "h-14 rounded-md px-6 pt-0.5 drop-shadow-lg",
        small:
          "size-10 shrink-0 rounded-lg drop-shadow-sm text-sm inset-ring-3",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

type ButtonStateProps = {
  pending?: boolean;
  processing?: boolean;
  errorMessage?: string;
  errorMessageTimer?: number;
};

function Button({
  className,
  variant,
  size,
  asChild = false,
  pending,
  processing,
  disabled,
  children,
  type = "button",
  errorMessage,
  errorMessageTimer = 10000,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> &
  ButtonStateProps & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";
  const isBusy = Boolean(pending || processing);
  const indicatorSize = size === "small" ? 16 : 20;

  // State for tooltip visibility with timer
  const [showTooltip, setShowTooltip] = React.useState(
    errorMessage ? true : false,
  );

  React.useEffect(() => {
    if (errorMessageTimer === 0) return;

    if (errorMessage) {
      setShowTooltip(true);
      const timer = setTimeout(() => {
        setShowTooltip(false);
      }, errorMessageTimer);

      return () => clearTimeout(timer);
    } else {
      setShowTooltip(false);
    }
  }, [errorMessage, errorMessageTimer]);

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // If there's an error message, show tooltip again
    if (errorMessage) {
      setShowTooltip(true);
      if (errorMessageTimer > 0) {
        setTimeout(() => {
          setShowTooltip(false);
        }, errorMessageTimer);
      }
    }
    
    // Call original onClick if provided
    props.onClick?.(e);
  };

  const buttonContent = (
    <>
      {/* Overlay icon when busy */}
      {isBusy &&
        (pending ? (
          <BouncingCircles
            size={indicatorSize}
            className="absolute fill-current stroke-current size-10"
          />
        ) : (
          <FadeStaggerCircles
            size={indicatorSize}
            className="absolute fill-current stroke-current size-11"
          />
        ))}
      {/* Keep original width by hiding text visually when busy */}
      <span className={cn(isBusy && "invisible")}>{children}</span>
    </>
  );

  if (errorMessage) {
    return (
      <Comp
        data-slot="button"
        className={cn(buttonVariants({ variant, size, className }))}
        disabled={disabled || isBusy}
        type={type}
        {...props}
        onClick={handleClick}
      >
        <TooltipNotification
          open={showTooltip}
          message={errorMessage}
          tooltipContentProps={{ alignOffset: -60, align: "end" }}
        >
          <span className="absolute top-0 right-7"></span>
        </TooltipNotification>
        {buttonContent}
      </Comp>
    );
  }

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      disabled={disabled || isBusy}
      type={type}
      {...props}
    >
      {buttonContent}
    </Comp>
  );
}

export { Button, buttonVariants };
